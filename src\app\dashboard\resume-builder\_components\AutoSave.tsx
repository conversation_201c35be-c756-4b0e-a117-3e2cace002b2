"use client";
import { useEffect, useState } from 'react';
import { useGlobalStore } from '@/store/useGlobalStore';
import { motion, AnimatePresence } from 'motion/react';
import { Save, Check, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AutoSaveStatus {
  status: 'idle' | 'saving' | 'saved' | 'error';
  lastSaved?: Date;
  error?: string;
}

export function AutoSave() {
  const { resumeBuilder } = useGlobalStore();
  const [saveStatus, setSaveStatus] = useState<AutoSaveStatus>({ status: 'idle' });
  const [lastDataSnapshot, setLastDataSnapshot] = useState<string>('');

  // Auto-save functionality
  useEffect(() => {
    const currentDataSnapshot = JSON.stringify(resumeBuilder.data);
    
    // Only save if data has actually changed
    if (currentDataSnapshot !== lastDataSnapshot && lastDataSnapshot !== '') {
      setSaveStatus({ status: 'saving' });
      
      // Simulate auto-save delay (in real app, this would be an API call)
      const saveTimer = setTimeout(() => {
        try {
          // In a real application, you would save to a backend here
          // For now, we'll just update localStorage (which is already handled by zustand persist)
          localStorage.setItem('resume-autosave', currentDataSnapshot);
          
          setSaveStatus({ 
            status: 'saved', 
            lastSaved: new Date() 
          });
          
          // Reset to idle after showing saved status
          setTimeout(() => {
            setSaveStatus(prev => ({ ...prev, status: 'idle' }));
          }, 2000);
          
        } catch (error) {
          setSaveStatus({ 
            status: 'error', 
            error: 'Failed to save changes' 
          });
          
          // Reset to idle after showing error
          setTimeout(() => {
            setSaveStatus(prev => ({ ...prev, status: 'idle' }));
          }, 3000);
        }
      }, 1000); // 1 second delay for auto-save

      return () => clearTimeout(saveTimer);
    }
    
    setLastDataSnapshot(currentDataSnapshot);
  }, [resumeBuilder.data, lastDataSnapshot]);

  // Don't render anything if idle
  if (saveStatus.status === 'idle') {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="fixed top-4 right-4 z-50"
      >
        <div
          className={cn(
            "flex items-center space-x-2 px-4 py-2 rounded-lg shadow-lg backdrop-blur-sm border",
            saveStatus.status === 'saving' && "bg-blue-50/90 border-blue-200 text-blue-700",
            saveStatus.status === 'saved' && "bg-green-50/90 border-green-200 text-green-700",
            saveStatus.status === 'error' && "bg-red-50/90 border-red-200 text-red-700"
          )}
        >
          {saveStatus.status === 'saving' && (
            <>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Save className="w-4 h-4" />
              </motion.div>
              <span className="text-sm font-medium">Saving...</span>
            </>
          )}
          
          {saveStatus.status === 'saved' && (
            <>
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              >
                <Check className="w-4 h-4" />
              </motion.div>
              <span className="text-sm font-medium">
                Saved {saveStatus.lastSaved?.toLocaleTimeString()}
              </span>
            </>
          )}
          
          {saveStatus.status === 'error' && (
            <>
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm font-medium">
                {saveStatus.error || 'Save failed'}
              </span>
            </>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

// Hook for manual save functionality
export function useManualSave() {
  const { resumeBuilder } = useGlobalStore();
  const [isSaving, setIsSaving] = useState(false);

  const saveManually = async () => {
    setIsSaving(true);
    try {
      // In a real application, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 500));
      localStorage.setItem('resume-manual-save', JSON.stringify(resumeBuilder.data));
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to save resume' };
    } finally {
      setIsSaving(false);
    }
  };

  return { saveManually, isSaving };
}

// Component for manual save button
export function ManualSaveButton() {
  const { saveManually, isSaving } = useManualSave();
  const [saveResult, setSaveResult] = useState<{ success: boolean; error?: string } | null>(null);

  const handleSave = async () => {
    const result = await saveManually();
    setSaveResult(result);
    
    // Clear result after 3 seconds
    setTimeout(() => setSaveResult(null), 3000);
  };

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={handleSave}
        disabled={isSaving}
        className={cn(
          "flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
          "bg-blue-600 hover:bg-blue-700 text-white shadow-sm",
          "disabled:opacity-50 disabled:cursor-not-allowed"
        )}
      >
        {isSaving ? (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <Save className="w-4 h-4" />
          </motion.div>
        ) : (
          <Save className="w-4 h-4" />
        )}
        <span>{isSaving ? 'Saving...' : 'Save Resume'}</span>
      </button>
      
      {saveResult && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className={cn(
            "flex items-center space-x-1 px-2 py-1 rounded text-xs",
            saveResult.success 
              ? "bg-green-100 text-green-700" 
              : "bg-red-100 text-red-700"
          )}
        >
          {saveResult.success ? (
            <Check className="w-3 h-3" />
          ) : (
            <AlertCircle className="w-3 h-3" />
          )}
          <span>{saveResult.success ? 'Saved!' : saveResult.error}</span>
        </motion.div>
      )}
    </div>
  );
}
