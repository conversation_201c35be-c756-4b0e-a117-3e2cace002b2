"use client";
import { useState } from 'react';
import { useGlobalStore } from '@/store/useGlobalStore';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '@/lib/utils';
import { 
  Download, 
  FileText, 
  Printer, 
  Share2, 
  Mail, 
  Link, 
  Check,
  Loader2,
  Settings,
  FileImage,
  FileType
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ExportFormat {
  id: 'pdf' | 'docx' | 'png' | 'txt';
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  available: boolean;
}

const exportFormats: ExportFormat[] = [
  {
    id: 'pdf',
    name: 'PDF Document',
    description: 'Best for applications and printing',
    icon: FileText,
    color: 'red',
    available: true
  },
  {
    id: 'docx',
    name: 'Word Document',
    description: 'Editable format for further customization',
    icon: FileType,
    color: 'blue',
    available: false // Coming soon
  },
  {
    id: 'png',
    name: 'PNG Image',
    description: 'High-quality image for portfolios',
    icon: FileImage,
    color: 'green',
    available: false // Coming soon
  },
  {
    id: 'txt',
    name: 'Plain Text',
    description: 'ATS-friendly text format',
    icon: FileText,
    color: 'gray',
    available: false // Coming soon
  }
];

interface ExportOptionsProps {
  onClose?: () => void;
}

export function ExportOptions({ onClose }: ExportOptionsProps) {
  const { resumeBuilder } = useGlobalStore();
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat['id']>('pdf');
  const [isExporting, setIsExporting] = useState(false);
  const [exportSuccess, setExportSuccess] = useState(false);
  const [shareOptions, setShareOptions] = useState(false);

  const handleExport = async (format: ExportFormat['id']) => {
    setIsExporting(true);
    
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (format === 'pdf') {
        // In a real app, this would generate and download the PDF
        const filename = `${resumeBuilder.data.personalInfo.fullName || 'Resume'}_Resume.pdf`;
        
        // Create a mock download
        const link = document.createElement('a');
        link.href = '#'; // In real app, this would be the PDF blob URL
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
      
      setExportSuccess(true);
      setTimeout(() => {
        setExportSuccess(false);
        onClose?.();
      }, 2000);
      
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleShare = (method: 'email' | 'link') => {
    if (method === 'email') {
      const subject = encodeURIComponent('My Resume');
      const body = encodeURIComponent('Please find my resume attached.');
      window.open(`mailto:?subject=${subject}&body=${body}`);
    } else if (method === 'link') {
      // In a real app, this would generate a shareable link
      navigator.clipboard.writeText('https://example.com/resume/shared-link');
      alert('Shareable link copied to clipboard!');
    }
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="bg-white rounded-xl shadow-xl border border-gray-200 p-6 max-w-md w-full"
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Export Resume</h3>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Export Formats */}
      <div className="space-y-3 mb-6">
        <h4 className="text-sm font-medium text-gray-700">Choose Format</h4>
        {exportFormats.map((format) => {
          const IconComponent = format.icon;
          const isSelected = selectedFormat === format.id;
          
          return (
            <motion.button
              key={format.id}
              onClick={() => format.available && setSelectedFormat(format.id)}
              disabled={!format.available}
              className={cn(
                "w-full p-3 rounded-lg border-2 transition-all duration-200 text-left",
                format.available
                  ? "hover:shadow-sm cursor-pointer"
                  : "opacity-50 cursor-not-allowed",
                isSelected && format.available
                  ? "border-indigo-500 bg-indigo-50"
                  : "border-gray-200 hover:border-gray-300"
              )}
              whileHover={format.available ? { scale: 1.02 } : {}}
              whileTap={format.available ? { scale: 0.98 } : {}}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    "w-8 h-8 rounded-lg flex items-center justify-center",
                    format.color === 'red' && "bg-red-100 text-red-600",
                    format.color === 'blue' && "bg-blue-100 text-blue-600",
                    format.color === 'green' && "bg-green-100 text-green-600",
                    format.color === 'gray' && "bg-gray-100 text-gray-600"
                  )}>
                    <IconComponent className="w-4 h-4" />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{format.name}</span>
                      {!format.available && (
                        <span className="text-xs bg-amber-100 text-amber-700 px-2 py-0.5 rounded-full">
                          Coming Soon
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-500">{format.description}</p>
                  </div>
                </div>
                {isSelected && format.available && (
                  <Check className="w-5 h-5 text-indigo-600" />
                )}
              </div>
            </motion.button>
          );
        })}
      </div>

      {/* Action Buttons */}
      <div className="space-y-3">
        <Button
          onClick={() => handleExport(selectedFormat)}
          disabled={isExporting || !exportFormats.find(f => f.id === selectedFormat)?.available}
          className="w-full flex items-center justify-center space-x-2"
        >
          {isExporting ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : exportSuccess ? (
            <Check className="w-4 h-4" />
          ) : (
            <Download className="w-4 h-4" />
          )}
          <span>
            {isExporting ? 'Exporting...' : exportSuccess ? 'Exported!' : 'Download Resume'}
          </span>
        </Button>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handlePrint}
            className="flex-1 flex items-center justify-center space-x-2"
          >
            <Printer className="w-4 h-4" />
            <span>Print</span>
          </Button>
          
          <Button
            variant="outline"
            onClick={() => setShareOptions(!shareOptions)}
            className="flex-1 flex items-center justify-center space-x-2"
          >
            <Share2 className="w-4 h-4" />
            <span>Share</span>
          </Button>
        </div>

        {/* Share Options */}
        <AnimatePresence>
          {shareOptions && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t pt-3 space-y-2"
            >
              <button
                onClick={() => handleShare('email')}
                className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Mail className="w-4 h-4 text-gray-600" />
                <span className="text-sm text-gray-700">Share via Email</span>
              </button>
              <button
                onClick={() => handleShare('link')}
                className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Link className="w-4 h-4 text-gray-600" />
                <span className="text-sm text-gray-700">Copy Shareable Link</span>
              </button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Export Settings */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="mt-6 pt-4 border-t border-gray-200"
      >
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Settings className="w-4 h-4" />
          <span>Export optimized for ATS compatibility</span>
        </div>
      </motion.div>
    </motion.div>
  );
}
