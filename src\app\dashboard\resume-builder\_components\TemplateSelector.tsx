"use client";
import { useState } from 'react';
import { useGlobalStore } from '@/store/useGlobalStore';
import { motion } from 'motion/react';
import { cn } from '@/lib/utils';
import { Check, Eye, Palette, FileText, Sparkles, Minimize2 } from 'lucide-react';

interface Template {
  id: 'modern' | 'classic' | 'creative' | 'minimal';
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  preview: string;
  features: string[];
  color: string;
  bgGradient: string;
}

const templates: Template[] = [
  {
    id: 'modern',
    name: 'Modern Professional',
    description: 'Clean, contemporary design with subtle colors and modern typography',
    icon: Sparkles,
    preview: '/templates/modern-preview.png',
    features: ['ATS-Friendly', 'Color Accents', 'Modern Typography', 'Clean Layout'],
    color: 'indigo',
    bgGradient: 'from-indigo-500 to-blue-600'
  },
  {
    id: 'classic',
    name: 'Classic Traditional',
    description: 'Traditional format preferred by conservative industries',
    icon: FileText,
    preview: '/templates/classic-preview.png',
    features: ['Traditional Format', 'Black & White', 'Professional', 'Time-tested'],
    color: 'gray',
    bgGradient: 'from-gray-600 to-gray-800'
  },
  {
    id: 'creative',
    name: 'Creative Designer',
    description: 'Bold design for creative professionals and designers',
    icon: Palette,
    preview: '/templates/creative-preview.png',
    features: ['Creative Layout', 'Bold Colors', 'Visual Elements', 'Stand Out'],
    color: 'purple',
    bgGradient: 'from-purple-500 to-pink-600'
  },
  {
    id: 'minimal',
    name: 'Minimal Clean',
    description: 'Ultra-clean design focusing on content over decoration',
    icon: Minimize2,
    preview: '/templates/minimal-preview.png',
    features: ['Ultra Clean', 'Content Focus', 'Minimal Design', 'Easy to Read'],
    color: 'emerald',
    bgGradient: 'from-emerald-500 to-teal-600'
  }
];

export function TemplateSelector() {
  const { resumeBuilder, setResumeTemplate } = useGlobalStore();
  const [selectedTemplate, setSelectedTemplate] = useState(resumeBuilder.data.template);
  const [hoveredTemplate, setHoveredTemplate] = useState<string | null>(null);

  const handleTemplateSelect = (templateId: Template['id']) => {
    setSelectedTemplate(templateId);
    setResumeTemplate(templateId);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Choose Your Template</h2>
        <p className="text-sm text-gray-600">
          Select a professional template that matches your industry and personal style.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {templates.map((template, index) => {
          const isSelected = selectedTemplate === template.id;
          const isHovered = hoveredTemplate === template.id;
          const IconComponent = template.icon;

          return (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative"
            >
              <motion.button
                onClick={() => handleTemplateSelect(template.id)}
                onMouseEnter={() => setHoveredTemplate(template.id)}
                onMouseLeave={() => setHoveredTemplate(null)}
                className={cn(
                  "w-full p-6 rounded-xl border-2 transition-all duration-300 text-left group",
                  "hover:shadow-lg hover:scale-[1.02]",
                  isSelected
                    ? "border-indigo-500 bg-indigo-50 shadow-lg"
                    : "border-gray-200 bg-white hover:border-gray-300"
                )}
                whileHover={{ y: -2 }}
                whileTap={{ scale: 0.98 }}
              >
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div
                      className={cn(
                        "w-12 h-12 rounded-lg flex items-center justify-center text-white transition-all duration-300",
                        `bg-gradient-to-r ${template.bgGradient}`,
                        isHovered && "scale-110"
                      )}
                    >
                      <IconComponent className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">
                        {template.name}
                      </h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {template.description}
                      </p>
                    </div>
                  </div>

                  {/* Selection Indicator */}
                  <motion.div
                    className={cn(
                      "w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200",
                      isSelected
                        ? "border-indigo-500 bg-indigo-500"
                        : "border-gray-300 group-hover:border-indigo-400"
                    )}
                    animate={isSelected ? { scale: [1, 1.2, 1] } : {}}
                    transition={{ duration: 0.3 }}
                  >
                    {isSelected && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      >
                        <Check className="w-4 h-4 text-white" />
                      </motion.div>
                    )}
                  </motion.div>
                </div>

                {/* Features */}
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2">
                    {template.features.map((feature, featureIndex) => (
                      <motion.span
                        key={feature}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: (index * 0.1) + (featureIndex * 0.05) }}
                        className={cn(
                          "px-2 py-1 text-xs font-medium rounded-full transition-colors",
                          isSelected
                            ? "bg-indigo-100 text-indigo-700"
                            : "bg-gray-100 text-gray-600 group-hover:bg-gray-200"
                        )}
                      >
                        {feature}
                      </motion.span>
                    ))}
                  </div>

                  {/* Preview Button */}
                  <div className="flex items-center justify-between pt-2">
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <Eye className="w-4 h-4" />
                      <span>Click to preview</span>
                    </div>
                    
                    {isSelected && (
                      <motion.div
                        initial={{ opacity: 0, x: 10 }}
                        animate={{ opacity: 1, x: 0 }}
                        className="flex items-center space-x-1 text-sm font-medium text-indigo-600"
                      >
                        <Check className="w-4 h-4" />
                        <span>Selected</span>
                      </motion.div>
                    )}
                  </div>
                </div>

                {/* Hover Effect Overlay */}
                <motion.div
                  className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent"
                  initial={{ x: '-100%' }}
                  animate={isHovered ? { x: '100%' } : { x: '-100%' }}
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                />
              </motion.button>
            </motion.div>
          );
        })}
      </div>

      {/* Template Info */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="bg-blue-50 border border-blue-200 rounded-lg p-4"
      >
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <FileText className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <h4 className="font-medium text-blue-900 mb-1">Template Selection Tips</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Modern:</strong> Great for tech, startups, and progressive companies</li>
              <li>• <strong>Classic:</strong> Perfect for finance, law, and traditional industries</li>
              <li>• <strong>Creative:</strong> Ideal for design, marketing, and creative roles</li>
              <li>• <strong>Minimal:</strong> Excellent for any industry, focuses on content</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
