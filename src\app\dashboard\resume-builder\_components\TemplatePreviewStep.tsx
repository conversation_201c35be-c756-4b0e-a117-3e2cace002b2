"use client";
import { useState } from 'react';
import { useGlobalStore } from '@/store/useGlobalStore';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'motion/react';
import { CheckCircle, AlertCircle, User, Briefcase, GraduationCap, Zap, Download, Palette } from 'lucide-react';
import { ExportOptions } from './ExportOptions';
import { validateAllSteps, getOverallProgress } from './validation';

export function TemplatePreviewStep() {
  const { resumeBuilder } = useGlobalStore();
  const { data, isLoading } = resumeBuilder;
  const [showExportOptions, setShowExportOptions] = useState(false);

  // Get validation status for all steps
  const validation = validateAllSteps(data);
  const overallProgress = getOverallProgress(validation);

  // Use validation system for completion status
  const sectionStatus = {
    personalInfo: {
      isComplete: validation[1].isValid,
      count: data.personalInfo.fullName ? 1 : 0
    },
    experience: {
      isComplete: validation[2].isValid,
      count: data.experience.length
    },
    education: {
      isComplete: validation[3].isValid,
      count: data.education.length
    },
    skills: {
      isComplete: validation[4].isValid,
      count: data.skills.length
    },
    template: {
      isComplete: validation[5].isValid,
      count: data.template ? 1 : 0
    }
  };

  const totalSections = Object.values(sectionStatus).length;
  const completedSections = Object.values(sectionStatus).filter(section => section.isComplete).length;
  const completionPercentage = overallProgress;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Review Your Resume</h2>
        <p className="text-sm text-gray-600">
          Review all sections of your resume and make sure everything looks perfect before downloading.
        </p>
      </div>

      {/* Completion Progress */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium text-gray-900">Resume Completion</h3>
          <span className="text-sm font-medium text-indigo-600">{Math.round(completionPercentage)}% Complete</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div 
            className="bg-gradient-to-r from-blue-600 to-indigo-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          />
        </div>
        <p className="text-xs text-gray-600">
          {completedSections} of {totalSections} sections completed
        </p>
      </div>

      {/* Section Details */}
      <div className="space-y-4">
        <h3 className="font-medium text-gray-900">Section Details</h3>
        
        {/* Personal Information */}
        <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
          <div className="flex items-center space-x-3">
            <User className="w-5 h-5 text-blue-600" />
            <div>
              <h4 className="font-medium text-gray-900">Personal Information</h4>
              <p className="text-sm text-gray-600">
                {data.personalInfo.fullName || 'No name provided'}
              </p>
            </div>
          </div>
          <div className="flex items-center">
            {sectionStatus.personalInfo.isComplete ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : (
              <AlertCircle className="w-5 h-5 text-amber-500" />
            )}
          </div>
        </div>

        {/* Experience */}
        <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
          <div className="flex items-center space-x-3">
            <Briefcase className="w-5 h-5 text-blue-600" />
            <div>
              <h4 className="font-medium text-gray-900">Work Experience</h4>
              <p className="text-sm text-gray-600">
                {sectionStatus.experience.count} position{sectionStatus.experience.count !== 1 ? 's' : ''} added
              </p>
            </div>
          </div>
          <div className="flex items-center">
            {sectionStatus.experience.isComplete ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : (
              <AlertCircle className="w-5 h-5 text-amber-500" />
            )}
          </div>
        </div>

        {/* Education */}
        <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
          <div className="flex items-center space-x-3">
            <GraduationCap className="w-5 h-5 text-blue-600" />
            <div>
              <h4 className="font-medium text-gray-900">Education</h4>
              <p className="text-sm text-gray-600">
                {sectionStatus.education.count} education entr{sectionStatus.education.count !== 1 ? 'ies' : 'y'} added
              </p>
            </div>
          </div>
          <div className="flex items-center">
            {sectionStatus.education.isComplete ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : (
              <AlertCircle className="w-5 h-5 text-amber-500" />
            )}
          </div>
        </div>

        {/* Skills */}
        <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
          <div className="flex items-center space-x-3">
            <Zap className="w-5 h-5 text-blue-600" />
            <div>
              <h4 className="font-medium text-gray-900">Skills</h4>
              <p className="text-sm text-gray-600">
                {sectionStatus.skills.count} skill{sectionStatus.skills.count !== 1 ? 's' : ''} added
              </p>
            </div>
          </div>
          <div className="flex items-center">
            {sectionStatus.skills.isComplete ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : (
              <AlertCircle className="w-5 h-5 text-amber-500" />
            )}
          </div>
        </div>

        {/* Template */}
        <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
          <div className="flex items-center space-x-3">
            <Palette className="w-5 h-5 text-blue-600" />
            <div>
              <h4 className="font-medium text-gray-900">Template</h4>
              <p className="text-sm text-gray-600">
                {data.template ? `${data.template.charAt(0).toUpperCase() + data.template.slice(1)} template selected` : 'No template selected'}
              </p>
            </div>
          </div>
          <div className="flex items-center">
            {sectionStatus.template.isComplete ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : (
              <AlertCircle className="w-5 h-5 text-amber-500" />
            )}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="space-y-3">
        <Button
          onClick={() => setShowExportOptions(true)}
          disabled={isLoading || completionPercentage < 80}
          className="w-full flex items-center justify-center space-x-2"
          size="lg"
        >
          <Download className="w-5 h-5" />
          <span>{isLoading ? 'Generating...' : 'Export Resume'}</span>
        </Button>

        {completionPercentage < 80 && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <p className="text-sm text-amber-700 text-center">
              Complete at least 80% of your resume to enable export
            </p>
            <div className="mt-2">
              <div className="w-full bg-amber-200 rounded-full h-2">
                <div
                  className="bg-amber-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${completionPercentage}%` }}
                />
              </div>
              <p className="text-xs text-amber-600 text-center mt-1">
                {Math.round(completionPercentage)}% complete
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Export Options Modal */}
      <AnimatePresence>
        {showExportOptions && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowExportOptions(false)}
          >
            <div onClick={(e) => e.stopPropagation()}>
              <ExportOptions onClose={() => setShowExportOptions(false)} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

     
    </motion.div>
  );
}
